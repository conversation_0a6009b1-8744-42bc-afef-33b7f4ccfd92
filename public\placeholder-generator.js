// Simple placeholder image generator for Node.js
const fs = require('fs');
const path = require('path');

// Create a simple SVG placeholder
function createSVGPlaceholder(width, height, text, gradient) {
  return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:${gradient[0]};stop-opacity:1" />
        <stop offset="100%" style="stop-color:${gradient[1]};stop-opacity:1" />
      </linearGradient>
    </defs>
    <rect width="100%" height="100%" fill="url(#grad)"/>
    <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="24" font-weight="bold" 
          text-anchor="middle" dominant-baseline="middle" fill="white">${text}</text>
  </svg>`;
}

// Generate placeholder images
const images = [
  { name: 'profile.jpg', width: 400, height: 400, text: 'MILAD', gradient: ['#0066ff', '#8b5cf6'] },
  { name: 'project-1.jpg', width: 600, height: 400, text: 'E-Commerce Platform', gradient: ['#0066ff', '#00d4ff'] },
  { name: 'project-2.jpg', width: 600, height: 400, text: '3D Portfolio Website', gradient: ['#8b5cf6', '#f472b6'] },
  { name: 'project-3.jpg', width: 600, height: 400, text: 'AI Dashboard', gradient: ['#06ffa5', '#00d4ff'] },
  { name: 'project-4.jpg', width: 600, height: 400, text: 'Mobile Banking App', gradient: ['#ff6b35', '#f472b6'] },
  { name: 'project-5.jpg', width: 600, height: 400, text: 'Social Media Platform', gradient: ['#8b5cf6', '#0066ff'] },
  { name: 'project-6.jpg', width: 600, height: 400, text: 'VR Experience', gradient: ['#00d4ff', '#06ffa5'] }
];

images.forEach(img => {
  const svg = createSVGPlaceholder(img.width, img.height, img.text, img.gradient);
  const filename = img.name.replace('.jpg', '.svg');
  fs.writeFileSync(path.join(__dirname, filename), svg);
  console.log(`Generated ${filename}`);
});

console.log('All placeholder images generated!');
