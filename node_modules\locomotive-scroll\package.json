{"name": "locomotive-scroll", "version": "4.1.4", "description": "Detection of elements in viewport & smooth scrolling with parallax effects.", "repository": "locomotivemtl/locomotive-scroll", "author": "Locomotive <<EMAIL>> (https://locomotive.ca)", "license": "MIT", "main": "./dist/locomotive-scroll.js", "module": "./dist/locomotive-scroll.esm.js", "scripts": {"start": "gulp", "format": "prettier --write 'src/**/*.js'", "build": "npm run format && gulp build"}, "devDependencies": {"@modularbp/gulp": "^1.0.5", "@modularbp/gulp-build": "^1.1.0", "@modularbp/gulp-error": "^1.0.3", "@modularbp/gulp-js": "^1.0.8", "@modularbp/gulp-notify": "^1.0.3", "@modularbp/gulp-sass": "^1.0.7", "@modularbp/gulp-serve": "^1.0.5", "@modularbp/gulp-svg": "^1.0.5", "@modularbp/gulp-watch": "^1.0.5", "gulp-header": "^2.0.9", "mbp": "^1.3.0", "merge-stream": "^2.0.0", "node-sass": "^6.0.1", "normalize.css": "^8.0.1", "postcss": "^8.3.11", "prettier": "^2.1.2"}, "dependencies": {"bezier-easing": "^2.1.0", "smoothscroll-polyfill": "^0.4.4", "virtual-scroll": "^1.5.2"}}