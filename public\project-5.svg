<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="project5Grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0066ff;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#project5Grad)"/>
  <circle cx="130" cy="130" r="20" fill="rgba(255,255,255,0.1)"/>
  <circle cx="470" cy="270" r="35" fill="rgba(255,255,255,0.08)"/>
  <circle cx="520" cy="100" r="15" fill="rgba(255,255,255,0.12)"/>
  <text x="50%" y="45%" font-family="Inter, Arial, sans-serif" font-size="28" font-weight="bold" 
        text-anchor="middle" dominant-baseline="middle" fill="white">Social Media Platform</text>
  <text x="50%" y="55%" font-family="Inter, Arial, sans-serif" font-size="16" font-weight="normal" 
        text-anchor="middle" dominant-baseline="middle" fill="rgba(255,255,255,0.8)">Next-gen social experience</text>
</svg>
