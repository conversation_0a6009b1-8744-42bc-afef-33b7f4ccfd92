// CSS loaded separately
// GSAP loaded from CDN
// ScrollTrigger loaded from CDN
// LocomotiveScroll loaded from CDN

// Wait for GSAP to load from CDN
function waitForGSAP() {
  return new Promise((resolve) => {
    if (typeof gsap !== 'undefined') {
      gsap.registerPlugin(ScrollTrigger);
      resolve();
    } else {
      setTimeout(() => waitForGSAP().then(resolve), 100);
    }
  });
}

// Wait for LocomotiveScroll to load from CDN
function waitForLocoScroll() {
  return new Promise((resolve) => {
    if (typeof LocomotiveScroll !== 'undefined') {
      resolve();
    } else {
      setTimeout(() => waitForLocoScroll().then(resolve), 100);
    }
  });
}

// ===== GLOBAL VARIABLES =====
let locoScroll
let isLoaded = false

// ===== PRELOADER ANIMATION =====
class PreloaderAnimation {
  constructor() {
    this.preloader = document.getElementById('preloader')
    this.progressBar = document.getElementById('progressBar')
    this.progressPercent = document.getElementById('progressPercent')
    this.mainContent = document.getElementById('mainContent')

    this.init()
  }

  init() {
    // Set initial states
    gsap.set(this.mainContent, { opacity: 0, visibility: 'hidden' })

    // Start loading animation
    this.animateProgress()
  }

  animateProgress() {
    const tl = gsap.timeline()

    // Animate logo glow
    tl.to('.logo-glow', {
      scale: 1.2,
      opacity: 0.6,
      duration: 1,
      ease: 'power2.inOut',
      yoyo: true,
      repeat: -1
    }, 0)

    // Animate progress bar
    tl.to(this.progressBar, {
      width: '100%',
      duration: 3,
      ease: 'power2.out',
      onUpdate: () => {
        const progress = Math.round((this.progressBar.offsetWidth / this.progressBar.parentElement.offsetWidth) * 100)
        this.progressPercent.textContent = progress
      },
      onComplete: () => {
        this.completeLoading()
      }
    }, 0.5)
  }

  completeLoading() {
    const tl = gsap.timeline()

    // Fade out preloader
    tl.to(this.preloader, {
      opacity: 0,
      scale: 0.9,
      duration: 1,
      ease: 'power2.inOut',
      onComplete: () => {
        this.preloader.style.display = 'none'
        this.showMainContent()
      }
    })
  }

  showMainContent() {
    // Show main content
    gsap.set(this.mainContent, { opacity: 1, visibility: 'visible' })
    this.mainContent.classList.add('loaded')

    // Initialize other animations
    isLoaded = true
    new HeroAnimations()
    new NavigationHandler()
    new ScrollAnimations()

    // Initialize Locomotive Scroll after content is loaded
    setTimeout(() => {
      this.initLocomotiveScroll()
    }, 500)
  }

  initLocomotiveScroll() {
    locoScroll = new LocomotiveScroll({
      el: document.querySelector('[data-scroll-container]') || document.body,
      smooth: true,
      multiplier: 1,
      class: 'is-reveal'
    })

    // Update ScrollTrigger when Locomotive Scroll updates
    locoScroll.on('scroll', ScrollTrigger.update)

    // Setup ScrollTrigger to use Locomotive Scroll
    ScrollTrigger.scrollerProxy(locoScroll.el, {
      scrollTop(value) {
        return arguments.length ? locoScroll.scrollTo(value, 0, 0) : locoScroll.scroll.instance.scroll.y
      },
      getBoundingClientRect() {
        return { top: 0, left: 0, width: window.innerWidth, height: window.innerHeight }
      },
      pinType: locoScroll.el.style.transform ? 'transform' : 'fixed'
    })

    ScrollTrigger.addEventListener('refresh', () => locoScroll.update())
    ScrollTrigger.refresh()
  }
}

// ===== HERO ANIMATIONS =====
class HeroAnimations {
  constructor() {
    this.init()
  }

  init() {
    // Animate hero text elements
    const tl = gsap.timeline({ delay: 0.5 })

    // Animate headline lines
    tl.to('.line-1', {
      opacity: 1,
      y: 0,
      filter: 'blur(0px)',
      duration: 1,
      ease: 'power3.out'
    })
    .to('.line-2', {
      opacity: 1,
      y: 0,
      filter: 'blur(0px)',
      duration: 1,
      ease: 'power3.out'
    }, '-=0.5')

    // Animate subtitle
    .to('.hero-subtitle', {
      opacity: 1,
      y: 0,
      filter: 'blur(0px)',
      duration: 0.8,
      ease: 'power3.out'
    }, '-=0.3')

    // Animate CTA button
    .to('.cta-button', {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.8,
      ease: 'back.out(1.7)'
    }, '-=0.2')

    // Animate Spline container
    .fromTo('.spline-container', {
      opacity: 0,
      x: 100,
      filter: 'blur(10px)'
    }, {
      opacity: 0.8,
      x: 0,
      filter: 'blur(0px)',
      duration: 1.5,
      ease: 'power3.out'
    }, 0)

    // Setup Spline iframe loading
    this.setupSplineLoading()

    // Setup CTA button interactions
    this.setupCTAInteractions()
  }

  setupCTAInteractions() {
    const ctaButton = document.getElementById('ctaButton')

    ctaButton.addEventListener('mouseenter', () => {
      gsap.to(ctaButton, {
        scale: 1.05,
        y: -5,
        duration: 0.3,
        ease: 'power2.out'
      })
    })

    ctaButton.addEventListener('mouseleave', () => {
      gsap.to(ctaButton, {
        scale: 1,
        y: 0,
        duration: 0.3,
        ease: 'power2.out'
      })
    })

    ctaButton.addEventListener('click', () => {
      // Pulse animation on click
      gsap.to(ctaButton, {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: 'power2.inOut'
      })

      // Scroll to contact section
      if (locoScroll) {
        locoScroll.scrollTo('#contact')
      } else {
        document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })
      }
    })
  }

  setupSplineLoading() {
    const splineIframe = document.querySelector('.spline-container iframe')

    if (splineIframe) {
      // Add loading event listeners
      splineIframe.addEventListener('load', () => {
        // Fade in the iframe once loaded
        gsap.to(splineIframe, {
          opacity: 0.8,
          duration: 1,
          ease: 'power2.out'
        })

        // Mark as loaded
        splineIframe.setAttribute('data-loaded', 'true')
      })

      // Handle loading errors
      splineIframe.addEventListener('error', () => {
        console.warn('Spline 3D model failed to load')
        // Optionally show fallback content
      })

      // Set initial opacity
      gsap.set(splineIframe, { opacity: 0 })
    }
  }
}

// ===== NAVIGATION HANDLER =====
class NavigationHandler {
  constructor() {
    this.navbar = document.getElementById('navbar')
    this.navMenu = document.getElementById('navMenu')
    this.hamburger = document.getElementById('hamburger')
    this.navLinks = document.querySelectorAll('.nav-link')

    this.init()
  }

  init() {
    // Setup hamburger menu
    this.hamburger.addEventListener('click', () => {
      this.toggleMobileMenu()
    })

    // Setup nav link clicks
    this.navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        e.preventDefault()
        const target = link.getAttribute('href')
        this.scrollToSection(target)
        this.closeMobileMenu()
      })
    })

    // Setup scroll-based navbar styling
    this.setupScrollNavbar()
  }

  toggleMobileMenu() {
    this.navMenu.classList.toggle('active')
    this.hamburger.classList.toggle('active')
  }

  closeMobileMenu() {
    this.navMenu.classList.remove('active')
    this.hamburger.classList.remove('active')
  }

  scrollToSection(target) {
    if (locoScroll) {
      locoScroll.scrollTo(target)
    } else {
      document.querySelector(target).scrollIntoView({ behavior: 'smooth' })
    }
  }

  setupScrollNavbar() {
    let lastScrollY = 0

    const updateNavbar = () => {
      const scrollY = window.pageYOffset || document.documentElement.scrollTop

      if (scrollY > 100) {
        this.navbar.style.background = 'rgba(10, 10, 15, 0.95)'
        this.navbar.style.backdropFilter = 'blur(20px)'
      } else {
        this.navbar.style.background = 'rgba(10, 10, 15, 0.8)'
        this.navbar.style.backdropFilter = 'blur(20px)'
      }

      lastScrollY = scrollY
    }

    window.addEventListener('scroll', updateNavbar)
    if (locoScroll) {
      locoScroll.on('scroll', updateNavbar)
    }
  }
}

// ===== SCROLL ANIMATIONS =====
class ScrollAnimations {
  constructor() {
    this.init()
  }

  init() {
    // About section animations
    this.animateAboutSection()

    // Skills animations
    this.animateSkills()

    // Projects animations
    this.animateProjects()

    // Contact section animations
    this.animateContact()

    // Footer animations
    this.animateFooter()
  }

  animateAboutSection() {
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: '.about',
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse'
      }
    })

    tl.fromTo('.about .section-title', {
      opacity: 0,
      y: 50,
      filter: 'blur(10px)'
    }, {
      opacity: 1,
      y: 0,
      filter: 'blur(0px)',
      duration: 1,
      ease: 'power3.out'
    })
    .fromTo('.profile-container', {
      opacity: 0,
      x: -100,
      filter: 'blur(10px)'
    }, {
      opacity: 1,
      x: 0,
      filter: 'blur(0px)',
      duration: 1,
      ease: 'power3.out'
    }, '-=0.5')
    .fromTo('.about-text', {
      opacity: 0,
      x: 100,
      filter: 'blur(5px)'
    }, {
      opacity: 1,
      x: 0,
      filter: 'blur(0px)',
      duration: 1,
      ease: 'power3.out'
    }, '-=0.7')
  }

  animateSkills() {
    gsap.fromTo('.skill-item', {
      opacity: 0,
      y: 50,
      scale: 0.8
    }, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.6,
      stagger: 0.1,
      ease: 'back.out(1.7)',
      scrollTrigger: {
        trigger: '.skills-grid',
        start: 'top 80%',
        toggleActions: 'play none none reverse'
      }
    })
  }

  animateProjects() {
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: '.projects',
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse'
      }
    })

    tl.fromTo('.projects .section-title', {
      opacity: 0,
      y: 50,
      filter: 'blur(10px)'
    }, {
      opacity: 1,
      y: 0,
      filter: 'blur(0px)',
      duration: 1,
      ease: 'power3.out'
    })
    .fromTo('.project-card', {
      opacity: 0,
      y: 100,
      scale: 0.8
    }, {
      opacity: 1,
      y: 0,
      scale: 1,
      duration: 0.8,
      stagger: 0.2,
      ease: 'power3.out'
    }, '-=0.5')
  }

  animateContact() {
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: '.contact',
        start: 'top 80%',
        end: 'bottom 20%',
        toggleActions: 'play none none reverse'
      }
    })

    tl.fromTo('.contact .section-title', {
      opacity: 0,
      y: 50,
      filter: 'blur(10px)'
    }, {
      opacity: 1,
      y: 0,
      filter: 'blur(0px)',
      duration: 1,
      ease: 'power3.out'
    })
    .fromTo('.form-group', {
      opacity: 0,
      x: -50
    }, {
      opacity: 1,
      x: 0,
      duration: 0.6,
      stagger: 0.1,
      ease: 'power3.out'
    }, '-=0.5')
    .fromTo('.social-link', {
      opacity: 0,
      x: 50
    }, {
      opacity: 1,
      x: 0,
      duration: 0.6,
      stagger: 0.1,
      ease: 'power3.out'
    }, '-=0.8')
  }

  animateFooter() {
    gsap.fromTo('.footer-content', {
      opacity: 0,
      y: 50
    }, {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: 'power3.out',
      scrollTrigger: {
        trigger: '.footer',
        start: 'top 90%',
        toggleActions: 'play none none reverse'
      }
    })
  }
}

// ===== FORM HANDLER =====
class FormHandler {
  constructor() {
    this.form = document.getElementById('contactForm')
    this.submitButton = this.form.querySelector('.submit-button')

    this.init()
  }

  init() {
    this.form.addEventListener('submit', (e) => {
      e.preventDefault()
      this.handleSubmit()
    })

    // Setup input focus animations
    this.setupInputAnimations()
  }

  setupInputAnimations() {
    const inputs = this.form.querySelectorAll('input, textarea')

    inputs.forEach(input => {
      input.addEventListener('focus', () => {
        gsap.to(input.nextElementSibling, {
          opacity: 0.1,
          duration: 0.3,
          ease: 'power2.out'
        })
      })

      input.addEventListener('blur', () => {
        gsap.to(input.nextElementSibling, {
          opacity: 0,
          duration: 0.3,
          ease: 'power2.out'
        })
      })
    })
  }

  async handleSubmit() {
    const formData = new FormData(this.form)
    const data = Object.fromEntries(formData)

    // Animate submit button
    gsap.to(this.submitButton, {
      scale: 0.95,
      duration: 0.1,
      yoyo: true,
      repeat: 1,
      ease: 'power2.inOut'
    })

    // Simulate form submission (replace with actual API call)
    try {
      this.submitButton.textContent = 'Sending...'
      this.submitButton.disabled = true

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Success animation
      gsap.to(this.submitButton, {
        backgroundColor: '#06ffa5',
        duration: 0.3,
        ease: 'power2.out'
      })

      this.submitButton.textContent = 'Message Sent!'

      // Reset form after delay
      setTimeout(() => {
        this.form.reset()
        this.submitButton.textContent = 'Send Message'
        this.submitButton.disabled = false
        gsap.to(this.submitButton, {
          backgroundColor: '',
          duration: 0.3,
          ease: 'power2.out'
        })
      }, 3000)

    } catch (error) {
      console.error('Form submission error:', error)
      this.submitButton.textContent = 'Error - Try Again'
      this.submitButton.disabled = false

      setTimeout(() => {
        this.submitButton.textContent = 'Send Message'
      }, 3000)
    }
  }
}

// ===== INTERACTIVE ELEMENTS =====
class InteractiveElements {
  constructor() {
    this.init()
  }

  init() {
    // Setup project card interactions
    this.setupProjectCards()

    // Setup skill item interactions
    this.setupSkillItems()

    // Setup social link interactions
    this.setupSocialLinks()
  }

  setupProjectCards() {
    const projectCards = document.querySelectorAll('.project-card')

    projectCards.forEach(card => {
      card.addEventListener('mouseenter', () => {
        gsap.to(card, {
          y: -15,
          scale: 1.02,
          duration: 0.3,
          ease: 'power2.out'
        })
      })

      card.addEventListener('mouseleave', () => {
        gsap.to(card, {
          y: 0,
          scale: 1,
          duration: 0.3,
          ease: 'power2.out'
        })
      })
    })
  }

  setupSkillItems() {
    const skillItems = document.querySelectorAll('.skill-item')

    skillItems.forEach(item => {
      item.addEventListener('mouseenter', () => {
        gsap.to(item, {
          y: -10,
          scale: 1.05,
          duration: 0.3,
          ease: 'back.out(1.7)'
        })
      })

      item.addEventListener('mouseleave', () => {
        gsap.to(item, {
          y: 0,
          scale: 1,
          duration: 0.3,
          ease: 'power2.out'
        })
      })
    })
  }

  setupSocialLinks() {
    const socialLinks = document.querySelectorAll('.social-link, .footer-social-link')

    socialLinks.forEach(link => {
      link.addEventListener('mouseenter', () => {
        gsap.to(link, {
          scale: 1.1,
          y: -5,
          duration: 0.3,
          ease: 'back.out(1.7)'
        })
      })

      link.addEventListener('mouseleave', () => {
        gsap.to(link, {
          scale: 1,
          y: 0,
          duration: 0.3,
          ease: 'power2.out'
        })
      })
    })
  }
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // Wait for CDN libraries to load
    await Promise.all([waitForGSAP(), waitForLocoScroll()]);

    // Start preloader animation
    new PreloaderAnimation()

    // Initialize form handler
    new FormHandler()

    // Initialize interactive elements
    new InteractiveElements()

    // Setup floating orbs animation
    gsap.to('.orb', {
      y: -20,
      duration: 3,
      repeat: -1,
      yoyo: true,
      ease: 'power1.inOut',
      stagger: 0.5
    })

    // Setup floating particles animation
    gsap.to('.particle', {
      y: -15,
      x: 10,
      duration: 4,
      repeat: -1,
      yoyo: true,
      ease: 'power1.inOut',
      stagger: 0.3
    })
  } catch (error) {
    console.error('Failed to initialize:', error);
    // Fallback: hide preloader and show content
    const preloader = document.getElementById('preloader');
    const mainContent = document.getElementById('mainContent');
    if (preloader) preloader.style.display = 'none';
    if (mainContent) {
      mainContent.style.opacity = '1';
      mainContent.style.visibility = 'visible';
    }
  }
})

// ===== WINDOW RESIZE HANDLER =====
window.addEventListener('resize', () => {
  if (locoScroll) {
    locoScroll.update()
  }
  ScrollTrigger.refresh()
})

// ===== PERFORMANCE OPTIMIZATION =====
// Preload critical images
const preloadImages = () => {
  const imageUrls = [
    '/profile.svg',
    '/project-1.svg',
    '/project-2.svg',
    '/project-3.svg',
    '/project-4.svg',
    '/project-5.svg',
    '/project-6.svg'
  ]

  imageUrls.forEach(url => {
    const img = new Image()
    img.src = url
  })
}

// Call preload function
preloadImages()
