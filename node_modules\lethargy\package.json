{"name": "lethargy", "version": "1.0.9", "description": "Distinguish between scroll events initiated by the user, and those by inertial scrolling", "main": "lethargy.js", "repository": {"type": "git", "url": "https://github.com/d4nyll/lethargy.git"}, "keywords": ["scroll", "wheel", "mouse", "mousewheel", "scrolljacking", "inertial", "trackpad", "magic", "mouse"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/d4nyll/lethargy/issues"}, "homepage": "https://github.com/d4nyll/lethargy"}