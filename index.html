<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Milad - Web Developer Portfolio</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/phosphor-icons@1.4.2/src/css/icons.css" rel="stylesheet">
  </head>
  <body>
    <!-- Preloader -->
    <div class="preloader" id="preloader">
      <div class="preloader-content">
        <div class="logo-animation">
          <h1 class="preloader-logo">Milad</h1>
          <div class="logo-glow"></div>
        </div>
        <div class="progress-container">
          <div class="progress-bar" id="progressBar"></div>
          <div class="progress-text">
            <span id="progressPercent">0</span>%
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
      <div class="nav-container">
        <div class="nav-logo">
          <span class="logo-text">Milad</span>
          <div class="logo-glow-nav"></div>
        </div>
        <div class="nav-menu" id="navMenu">
          <a href="#home" class="nav-link">Home</a>
          <a href="#about" class="nav-link">About</a>
          <a href="#projects" class="nav-link">Projects</a>
          <a href="#contact" class="nav-link">Contact</a>
        </div>
        <div class="hamburger" id="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content" id="mainContent">
      <!-- Hero Section -->
      <section class="hero" id="home">
        <div class="hero-background">
          <!-- Spline 3D Model Container -->
          <div class="spline-container">
            <spline-viewer url="https://prod.spline.design/6Eo1qMbqZpNGCzjJ/scene.splinecode"></spline-viewer>
          </div>
          <!-- Floating Neon Orbs -->
          <div class="floating-orbs">
            <div class="orb orb-1"></div>
            <div class="orb orb-2"></div>
            <div class="orb orb-3"></div>
            <div class="orb orb-4"></div>
          </div>
        </div>
        <div class="hero-content">
          <div class="hero-text">
            <h1 class="hero-headline">
              <span class="line-1">Hi, I'm Milad –</span>
              <span class="line-2">Web Developer</span>
            </h1>
            <p class="hero-subtitle">
              Crafting immersive digital experiences with cutting-edge technology
            </p>
            <button class="cta-button" id="ctaButton">
              <span class="cta-text">Hire Me</span>
              <div class="cta-glow"></div>
            </button>
          </div>
        </div>
      </section>

      <!-- About Section -->
      <section class="about" id="about">
        <div class="container">
          <div class="about-content">
            <div class="about-left">
              <div class="profile-container">
                <div class="profile-image">
                  <img src="/profile.svg" alt="Milad Profile" id="profileImg">
                  <div class="profile-glow"></div>
                </div>
              </div>
            </div>
            <div class="about-right">
              <h2 class="section-title">About Me</h2>
              <p class="about-text">
                I'm a passionate web developer with expertise in modern technologies.
                I create stunning, interactive experiences that push the boundaries of what's possible on the web.
              </p>
              <div class="skills-grid">
                <div class="skill-item">
                  <i class="ph-file-html"></i>
                  <span>HTML5</span>
                </div>
                <div class="skill-item">
                  <i class="ph-file-css"></i>
                  <span>CSS3</span>
                </div>
                <div class="skill-item">
                  <i class="ph-file-js"></i>
                  <span>JavaScript</span>
                </div>
                <div class="skill-item">
                  <i class="ph-atom"></i>
                  <span>React</span>
                </div>
                <div class="skill-item">
                  <i class="ph-lightning"></i>
                  <span>GSAP</span>
                </div>
                <div class="skill-item">
                  <i class="ph-cube"></i>
                  <span>Three.js</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Projects Section -->
      <section class="projects" id="projects">
        <div class="container">
          <h2 class="section-title">Featured Projects</h2>
          <div class="projects-container">
            <div class="project-card" data-project="1">
              <div class="project-image">
                <img src="/project-1.svg" alt="Project 1">
                <div class="project-overlay">
                  <div class="project-info">
                    <h3 class="project-title">E-Commerce Platform</h3>
                    <p class="project-description">Modern e-commerce solution with React and Node.js</p>
                    <div class="project-tech">
                      <span class="tech-tag">React</span>
                      <span class="tech-tag">Node.js</span>
                      <span class="tech-tag">MongoDB</span>
                    </div>
                    <button class="project-cta">
                      <span>View Project</span>
                      <i class="ph-arrow-up-right"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="project-card" data-project="2">
              <div class="project-image">
                <img src="/project-2.svg" alt="Project 2">
                <div class="project-overlay">
                  <div class="project-info">
                    <h3 class="project-title">3D Portfolio Website</h3>
                    <p class="project-description">Interactive 3D portfolio with Three.js and GSAP</p>
                    <div class="project-tech">
                      <span class="tech-tag">Three.js</span>
                      <span class="tech-tag">GSAP</span>
                      <span class="tech-tag">WebGL</span>
                    </div>
                    <button class="project-cta">
                      <span>View Project</span>
                      <i class="ph-arrow-up-right"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="project-card" data-project="3">
              <div class="project-image">
                <img src="/project-3.svg" alt="Project 3">
                <div class="project-overlay">
                  <div class="project-info">
                    <h3 class="project-title">AI Dashboard</h3>
                    <p class="project-description">Real-time AI analytics dashboard with data visualization</p>
                    <div class="project-tech">
                      <span class="tech-tag">Vue.js</span>
                      <span class="tech-tag">D3.js</span>
                      <span class="tech-tag">Python</span>
                    </div>
                    <button class="project-cta">
                      <span>View Project</span>
                      <i class="ph-arrow-up-right"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="project-card" data-project="4">
              <div class="project-image">
                <img src="/project-4.svg" alt="Project 4">
                <div class="project-overlay">
                  <div class="project-info">
                    <h3 class="project-title">Mobile Banking App</h3>
                    <p class="project-description">Secure mobile banking application with biometric auth</p>
                    <div class="project-tech">
                      <span class="tech-tag">React Native</span>
                      <span class="tech-tag">Firebase</span>
                      <span class="tech-tag">Stripe</span>
                    </div>
                    <button class="project-cta">
                      <span>View Project</span>
                      <i class="ph-arrow-up-right"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="project-card" data-project="5">
              <div class="project-image">
                <img src="/project-5.svg" alt="Project 5">
                <div class="project-overlay">
                  <div class="project-info">
                    <h3 class="project-title">Social Media Platform</h3>
                    <p class="project-description">Next-gen social platform with real-time features</p>
                    <div class="project-tech">
                      <span class="tech-tag">Next.js</span>
                      <span class="tech-tag">Socket.io</span>
                      <span class="tech-tag">PostgreSQL</span>
                    </div>
                    <button class="project-cta">
                      <span>View Project</span>
                      <i class="ph-arrow-up-right"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div class="project-card" data-project="6">
              <div class="project-image">
                <img src="/project-6.svg" alt="Project 6">
                <div class="project-overlay">
                  <div class="project-info">
                    <h3 class="project-title">VR Experience</h3>
                    <p class="project-description">Immersive VR web experience using WebXR</p>
                    <div class="project-tech">
                      <span class="tech-tag">WebXR</span>
                      <span class="tech-tag">A-Frame</span>
                      <span class="tech-tag">WebGL</span>
                    </div>
                    <button class="project-cta">
                      <span>View Project</span>
                      <i class="ph-arrow-up-right"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section class="contact" id="contact">
        <div class="container">
          <h2 class="section-title">Get In Touch</h2>
          <div class="contact-content">
            <div class="contact-form-container">
              <form class="contact-form" id="contactForm">
                <div class="form-group">
                  <input type="text" id="name" name="name" placeholder="Your Name" required>
                  <div class="input-glow"></div>
                </div>
                <div class="form-group">
                  <input type="email" id="email" name="email" placeholder="Your Email" required>
                  <div class="input-glow"></div>
                </div>
                <div class="form-group">
                  <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                  <div class="input-glow"></div>
                </div>
                <button type="submit" class="submit-button">
                  <span>Send Message</span>
                  <div class="submit-glow"></div>
                </button>
              </form>
            </div>
            <div class="contact-info">
              <div class="social-links">
                <a href="https://github.com" class="social-link" target="_blank">
                  <i class="ph-github-logo"></i>
                  <span>GitHub</span>
                </a>
                <a href="https://linkedin.com" class="social-link" target="_blank">
                  <i class="ph-linkedin-logo"></i>
                  <span>LinkedIn</span>
                </a>
                <a href="mailto:<EMAIL>" class="social-link">
                  <i class="ph-envelope"></i>
                  <span>Email</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Footer -->
      <footer class="footer">
        <div class="footer-particles">
          <div class="particle particle-1"></div>
          <div class="particle particle-2"></div>
          <div class="particle particle-3"></div>
          <div class="particle particle-4"></div>
          <div class="particle particle-5"></div>
        </div>
        <div class="container">
          <div class="footer-content">
            <div class="footer-nav">
              <a href="#home" class="footer-link">Home</a>
              <a href="#about" class="footer-link">About</a>
              <a href="#projects" class="footer-link">Projects</a>
              <a href="#contact" class="footer-link">Contact</a>
            </div>
            <div class="footer-social">
              <a href="https://github.com" class="footer-social-link" target="_blank">
                <i class="ph-github-logo"></i>
              </a>
              <a href="https://linkedin.com" class="footer-social-link" target="_blank">
                <i class="ph-linkedin-logo"></i>
              </a>
              <a href="mailto:<EMAIL>" class="footer-social-link">
                <i class="ph-envelope"></i>
              </a>
            </div>
            <div class="footer-text">
              <p>&copy; 2024 Milad. All rights reserved.</p>
            </div>
          </div>
        </div>
      </footer>
    </main>

    <!-- Spline Viewer Script -->
    <script type="module" src="https://unpkg.com/@splinetool/viewer@1.9.48/build/spline-viewer.js"></script>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
