{"name": "virtual-scroll", "version": "1.5.2", "description": "Smooth fake scroll using CSS transform", "main": "src/index.js", "scripts": {"test": "browserify test/index.js | tap-closer | smokestack | faucet", "test-debug": "budo test/index.js --live"}, "repository": {"type": "git", "url": "https://github.com/ayamflow/virtual-scroll"}, "keywords": ["virtual", "scroll", "smooth"], "author": "= <=>", "license": "MIT", "bugs": {"url": "https://github.com/ayamflow/virtual-scroll/issues"}, "homepage": "https://github.com/ayamflow/virtual-scroll", "dependencies": {"bindall-standalone": "^1.0.5", "lethargy": "^1.0.2", "object-assign": "^4.0.1", "tiny-emitter": "^1.0.0"}, "devDependencies": {"browserify": "^14.3.0", "budo": "^9.4.7", "faucet": "0.0.1", "smokestack": "^3.4.1", "tap-closer": "^1.0.0", "tape": "^4.6.3", "tiny-trigger": "scottcorgan/tiny-trigger"}}