#!/usr/bin/env python3
"""
Simple HTTP server for testing the portfolio website
"""
import http.server
import socketserver
import os
import sys
from pathlib import Path

# Set the port
PORT = 3000

# Change to the project directory
os.chdir(Path(__file__).parent)

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def guess_type(self, path):
        # Handle ES modules
        if path.endswith('.js'):
            return 'application/javascript'
        return super().guess_type(path)

# Create the server
with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
    print(f"🚀 Portfolio server running at http://localhost:{PORT}")
    print("📁 Serving files from current directory")
    print("🛑 Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped")
        sys.exit(0)
