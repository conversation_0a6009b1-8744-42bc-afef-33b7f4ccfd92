{"name": "tiny-emitter", "main": "dist/tinyemitter.js", "version": "1.2.0", "homepage": "https://github.com/scottcorgan/tiny-emitter", "authors": ["<PERSON> <<EMAIL>>"], "description": "A tiny (less than 1k) event emitter library", "keywords": ["emitter", "tiny", "event", "pubsub", "bind"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", ".giti<PERSON>re", "index.js", "package.json", "bower.json"]}