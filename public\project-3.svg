<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="project3Grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06ffa5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00d4ff;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#project3Grad)"/>
  <rect x="120" y="120" width="60" height="40" fill="rgba(255,255,255,0.1)"/>
  <rect x="420" y="240" width="80" height="50" fill="rgba(255,255,255,0.08)"/>
  <rect x="480" y="80" width="50" height="30" fill="rgba(255,255,255,0.12)"/>
  <text x="50%" y="45%" font-family="Inter, Arial, sans-serif" font-size="28" font-weight="bold" 
        text-anchor="middle" dominant-baseline="middle" fill="white">AI Dashboard</text>
  <text x="50%" y="55%" font-family="Inter, Arial, sans-serif" font-size="16" font-weight="normal" 
        text-anchor="middle" dominant-baseline="middle" fill="rgba(255,255,255,0.8)">Real-time analytics</text>
</svg>
