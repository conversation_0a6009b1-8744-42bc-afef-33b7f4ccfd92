<svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="profileGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0066ff;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00d4ff;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="circleGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:rgba(255,255,255,0.2);stop-opacity:1" />
      <stop offset="100%" style="stop-color:rgba(255,255,255,0);stop-opacity:1" />
    </radialGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#profileGrad)"/>
  <circle cx="200" cy="200" r="120" fill="url(#circleGrad)"/>
  <text x="50%" y="50%" font-family="Inter, Arial, sans-serif" font-size="36" font-weight="bold" 
        text-anchor="middle" dominant-baseline="middle" fill="white">MILAD</text>
  <text x="50%" y="65%" font-family="Inter, Arial, sans-serif" font-size="16" font-weight="normal" 
        text-anchor="middle" dominant-baseline="middle" fill="rgba(255,255,255,0.8)">Web Developer</text>
</svg>
