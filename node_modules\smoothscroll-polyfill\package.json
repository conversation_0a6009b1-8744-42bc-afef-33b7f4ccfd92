{"title": "smoothscroll", "name": "smoothscroll-polyfill", "description": "Smooth Scroll behavior polyfill", "version": "0.4.4", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://iamdustan.com"}, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://jeremenichelli.io"}], "dependencies": {}, "devDependencies": {"ava": "^0.25.0", "eslint": "^4.7.1", "husky": "^0.14.3", "lint-staged": "^6.0.1", "lodash.clonedeep": "^4.5.0", "prettier": "^1.10.2", "rollup": "^0.50.0", "uglify-js": "^3.1.1"}, "main": "dist/smoothscroll.js", "scripts": {"lint": "eslint {src,test}/**/*.js", "test": "ava test/smoothscroll.test.js --verbose", "posttest": "yarn lint", "precommit": "lint-staged", "prebuild": "npm test", "build": "rollup -c rollup.config.js", "preminify": "yarn build", "minify": "uglifyjs dist/smoothscroll.js -c -m -o dist/smoothscroll.min.js --verbose", "listdiff": "prettier --list-different {src,test}/**/*.js", "format": "prettier --write {src,test}/**/*.js", "prepublish": "npm run minify"}, "lint-staged": {"linters": {"{src,test}/**/*.js": ["prettier --list-different", "git add"]}}, "prettier": {"singleQuote": true}, "repository": {"type": "git", "url": "git+https://github.com/iamdustan/smoothscroll.git"}, "keywords": ["smooth", "scroll", "CSSOM", "polyfill"], "license": "MIT", "bugs": {"url": "https://github.com/iamdustan/smoothscroll/issues"}, "homepage": "https://iamdustan.com/smoothscroll"}