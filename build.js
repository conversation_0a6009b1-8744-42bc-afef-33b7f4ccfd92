const fs = require('fs');
const path = require('path');

// Create dist directory
const distDir = './dist';
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir);
}

// Copy HTML file
fs.copyFileSync('./index.html', path.join(distDir, 'index.html'));

// Copy CSS file
fs.copyFileSync('./src/style.css', path.join(distDir, 'style.css'));

// Copy JS file and modify imports for browser
let jsContent = fs.readFileSync('./src/main.js', 'utf8');

// Replace imports with CDN links
jsContent = jsContent.replace(
  `import { gsap } from 'gsap'`,
  `// GSAP loaded from CDN`
);
jsContent = jsContent.replace(
  `import { ScrollTrigger } from 'gsap/ScrollTrigger'`,
  `// ScrollTrigger loaded from CDN`
);
jsContent = jsContent.replace(
  `import LocomotiveScroll from 'locomotive-scroll'`,
  `// LocomotiveScroll loaded from CDN`
);

fs.writeFileSync(path.join(distDir, 'main.js'), jsContent);

// Copy public assets
const publicFiles = ['profile.svg', 'project-1.svg', 'project-2.svg', 'project-3.svg', 'project-4.svg', 'project-5.svg', 'project-6.svg'];
publicFiles.forEach(file => {
  if (fs.existsSync(path.join('./public', file))) {
    fs.copyFileSync(path.join('./public', file), path.join(distDir, file));
  }
});

// Update HTML to include CDN scripts and fix paths
let htmlContent = fs.readFileSync(path.join(distDir, 'index.html'), 'utf8');

// Add CDN scripts before closing head tag
const cdnScripts = `
  <!-- GSAP CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
  
  <!-- Locomotive Scroll CDN -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.css">
  <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.js"></script>
`;

htmlContent = htmlContent.replace('</head>', cdnScripts + '\n  </head>');

// Update script and CSS paths
htmlContent = htmlContent.replace('/src/main.js', './main.js');
htmlContent = htmlContent.replace('./style.css', './style.css');

// Add CSS import at the beginning of head
htmlContent = htmlContent.replace('<title>', '<link rel="stylesheet" href="./style.css">\n    <title>');

fs.writeFileSync(path.join(distDir, 'index.html'), htmlContent);

console.log('✅ Build completed successfully!');
console.log('📁 Files copied to ./dist directory');
console.log('🌐 You can now serve the ./dist directory');
console.log('');
console.log('To test locally, you can use:');
console.log('- Python: python -m http.server 3000 (from dist directory)');
console.log('- Node.js: npx serve dist');
console.log('- VS Code: Use Live Server extension on dist/index.html');
