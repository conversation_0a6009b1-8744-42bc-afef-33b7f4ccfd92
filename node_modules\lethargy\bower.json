{"name": "lethargy", "main": "lethargy.js", "version": "1.0.4", "homepage": "https://github.com/d4nyll/lethargy", "authors": ["<PERSON> <<EMAIL>>"], "description": "Distinguish between scroll events initiated by the user, and those by inertial scrolling", "keywords": ["scroll", "wheel", "mouse", "mousewheel", "scrolljacking", "inertial", "scroll", "trackpad", "magic", "mouse"], "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}