const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 8000;
const DIST_DIR = './dist';

// MIME types
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'application/javascript',
  '.css': 'text/css',
  '.svg': 'image/svg+xml',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.ico': 'image/x-icon',
  '.json': 'application/json'
};

const server = http.createServer((req, res) => {
  // Parse URL and remove query parameters
  let filePath = path.join(DIST_DIR, req.url.split('?')[0]);
  
  // Default to index.html
  if (filePath === path.join(DIST_DIR, '/')) {
    filePath = path.join(DIST_DIR, 'index.html');
  }

  // Get file extension
  const extname = String(path.extname(filePath)).toLowerCase();
  const mimeType = mimeTypes[extname] || 'application/octet-stream';

  // Check if file exists
  fs.access(filePath, fs.constants.F_OK, (err) => {
    if (err) {
      // File not found
      res.writeHead(404, { 'Content-Type': 'text/html' });
      res.end(`
        <h1>404 - File Not Found</h1>
        <p>The file <code>${req.url}</code> was not found.</p>
        <p><a href="/">Go back to homepage</a></p>
      `, 'utf-8');
      return;
    }

    // Read and serve file
    fs.readFile(filePath, (error, content) => {
      if (error) {
        res.writeHead(500);
        res.end(`Server Error: ${error.code}`, 'utf-8');
      } else {
        res.writeHead(200, { 
          'Content-Type': mimeType,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        });
        res.end(content, 'utf-8');
      }
    });
  });
});

// Check if dist directory exists
if (!fs.existsSync(DIST_DIR)) {
  console.log('❌ dist directory not found!');
  console.log('Please run "node build.cjs" first to create the dist folder.');
  process.exit(1);
}

server.listen(PORT, () => {
  console.log(`🚀 Portfolio server running at http://localhost:${PORT}`);
  console.log(`📁 Serving files from ${DIST_DIR}`);
  console.log('🌐 Open http://localhost:8000 in your browser');
  console.log('🛑 Press Ctrl+C to stop the server');
});
