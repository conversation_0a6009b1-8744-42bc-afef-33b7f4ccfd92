{"name": "bindall-standalone", "version": "1.0.5", "description": "Standalone, improved version of underscore's `_.bindAll()` function for IE9+ browsers.", "main": "index.js", "scripts": {"test": "node test/bindall.js | tap-spec"}, "repository": {"type": "git", "url": "https://github.com/ayamflow/bindall-standalone.git"}, "keywords": ["bind", "bindAll", "context", "call", "apply"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/ayamflow/bindall-standalone/issues"}, "devDependencies": {"tape": "~2.12.3", "tap-spec": "~0.1.9"}}