# 🚀 <PERSON><PERSON>'s Futuristic Portfolio Website

A cutting-edge, immersive portfolio website featuring modern animations, 3D integration, and premium design aesthetics.

## ✨ Features

### 🎨 Design & Aesthetics
- **Futuristic Design**: Modern blue/violet/neon color scheme with glassmorphic elements
- **Premium Typography**: Inter font family with carefully crafted spacing
- **Glassmorphic UI**: Translucent cards with backdrop blur effects
- **Neon Glows**: Dynamic glowing effects throughout the interface

### 🎭 Animations & Interactions
- **GSAP Loading Animation**: Cinematic preloader with progress bar
- **Scroll-Based Animations**: Smooth reveal animations triggered by scroll
- **Locomotive Scroll**: Buttery smooth parallax scrolling
- **Interactive Elements**: Hover effects, button animations, and micro-interactions
- **Floating Elements**: Animated background orbs and particles

### 🌐 3D Integration
- **Spline 3D Model**: Embedded 3D scene in hero section
- **WebGL Effects**: Hardware-accelerated graphics
- **Interactive 3D**: Responsive 3D elements

### 📱 Responsive Design
- **Mobile-First**: Optimized for all device sizes
- **Hamburger Navigation**: Smooth mobile menu transitions
- **Touch-Friendly**: Swipeable project cards on mobile
- **Adaptive Layouts**: Grid systems that adapt to screen size

### 🔧 Technical Features
- **Modern JavaScript**: ES6+ with modular architecture
- **Performance Optimized**: Lazy loading and efficient animations
- **Cross-Browser Compatible**: Works on all modern browsers
- **SEO Friendly**: Semantic HTML and meta tags

## 🏗️ Project Structure

```
├── dist/                 # Built files ready for deployment
│   ├── index.html       # Main HTML file with CDN links
│   ├── style.css        # Compiled CSS
│   ├── main.js          # JavaScript with CDN imports
│   └── *.svg            # Placeholder images
├── src/                 # Source files
│   ├── main.js          # Main JavaScript file
│   └── style.css        # Main stylesheet
├── public/              # Static assets
│   └── *.svg            # SVG placeholder images
├── index.html           # Development HTML
├── build.cjs            # Build script
└── package.json         # Dependencies
```

## 🚀 Quick Start

### Option 1: Use Built Files (Recommended)
The `dist/` folder contains production-ready files with CDN dependencies:

1. **Serve the dist folder**:
   ```bash
   # Using Python
   cd dist
   python -m http.server 3000
   
   # Using Node.js
   npx serve dist
   
   # Using VS Code Live Server
   # Right-click on dist/index.html and select "Open with Live Server"
   ```

2. **Open in browser**: `http://localhost:3000`

### Option 2: Development Setup
If you want to modify the source code:

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Build the project**:
   ```bash
   node build.cjs
   ```

3. **Serve the built files** (see Option 1)

## 🌐 Deployment Options

### GitHub Pages
1. **Create a new repository** on GitHub
2. **Upload the `dist/` folder contents** to the repository
3. **Enable GitHub Pages** in repository settings
4. **Set source** to main branch / root folder

### Netlify
1. **Drag and drop** the `dist/` folder to Netlify
2. **Or connect** your GitHub repository
3. **Set build command**: `node build.cjs`
4. **Set publish directory**: `dist`

### Vercel
1. **Import** your GitHub repository
2. **Set build command**: `node build.cjs`
3. **Set output directory**: `dist`

### Other Hosting Services
Upload the contents of the `dist/` folder to any static hosting service:
- Firebase Hosting
- AWS S3 + CloudFront
- Azure Static Web Apps
- Surge.sh

## 🎨 Customization

### Colors
Edit the CSS custom properties in `src/style.css`:
```css
:root {
  --primary-blue: #0066ff;
  --electric-blue: #00d4ff;
  --neon-purple: #8b5cf6;
  /* ... more colors */
}
```

### Content
Update the content in `index.html`:
- Personal information
- Project details
- Social media links
- Contact information

### Images
Replace the SVG placeholders in `public/` with your own images:
- `profile.svg` - Your profile photo
- `project-*.svg` - Your project screenshots

### Animations
Modify animations in `src/main.js`:
- Timing and easing functions
- Animation sequences
- Scroll trigger points

## 🔧 Dependencies

### Production (CDN)
- **GSAP 3.12.2**: Animation library
- **Locomotive Scroll 4.1.4**: Smooth scrolling
- **Phosphor Icons 1.4.2**: Icon library
- **Spline Viewer**: 3D model integration

### Development
- **Vite**: Build tool (optional)
- **Node.js**: For build scripts

## 📱 Browser Support
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🐛 Troubleshooting

### Common Issues
1. **Animations not working**: Ensure GSAP CDN is loaded
2. **3D model not showing**: Check Spline URL and internet connection
3. **Fonts not loading**: Verify Google Fonts connection
4. **Mobile layout issues**: Test responsive breakpoints

### Performance Tips
- Use WebP images for better compression
- Optimize SVG files
- Enable gzip compression on server
- Use a CDN for faster loading

## 📄 License
This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing
Feel free to fork this project and customize it for your own portfolio!

## 📞 Support
If you need help customizing this portfolio, feel free to reach out!

---

**Built with ❤️ using modern web technologies**
