<svg width="600" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="project4Grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b35;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f472b6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#project4Grad)"/>
  <ellipse cx="150" cy="120" rx="40" ry="25" fill="rgba(255,255,255,0.1)"/>
  <ellipse cx="450" cy="280" rx="50" ry="30" fill="rgba(255,255,255,0.08)"/>
  <text x="50%" y="45%" font-family="Inter, Arial, sans-serif" font-size="28" font-weight="bold" 
        text-anchor="middle" dominant-baseline="middle" fill="white">Mobile Banking App</text>
  <text x="50%" y="55%" font-family="Inter, Arial, sans-serif" font-size="16" font-weight="normal" 
        text-anchor="middle" dominant-baseline="middle" fill="rgba(255,255,255,0.8)">Secure financial platform</text>
</svg>
