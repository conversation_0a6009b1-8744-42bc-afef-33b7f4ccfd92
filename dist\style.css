/* ===== CSS RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Color Palette - Futuristic Blues, Violets, Neons */
  --primary-bg: #0a0a0f;
  --secondary-bg: #1a1a2e;
  --accent-bg: #16213e;

  --primary-blue: #0066ff;
  --electric-blue: #00d4ff;
  --neon-purple: #8b5cf6;
  --neon-pink: #f472b6;
  --neon-cyan: #06ffa5;
  --neon-orange: #ff6b35;

  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --text-muted: rgba(255, 255, 255, 0.5);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--neon-purple));
  --gradient-secondary: linear-gradient(135deg, var(--electric-blue), var(--neon-cyan));
  --gradient-accent: linear-gradient(135deg, var(--neon-pink), var(--neon-orange));

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing */
  --container-max-width: 1200px;
  --section-padding: 100px 0;
  --element-spacing: 2rem;

  /* Animations */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  font-family: var(--font-family);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--primary-bg);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 2rem;
}

.section-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: var(--font-weight-bold);
  text-align: center;
  margin-bottom: 4rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: var(--gradient-secondary);
  border-radius: 2px;
}

/* ===== PRELOADER STYLES ===== */
.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: var(--primary-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 1;
  visibility: visible;
}

.preloader-content {
  text-align: center;
  position: relative;
}

.logo-animation {
  position: relative;
  margin-bottom: 3rem;
}

.preloader-logo {
  font-size: 4rem;
  font-weight: var(--font-weight-bold);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 2;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, var(--electric-blue) 0%, transparent 70%);
  opacity: 0.3;
  border-radius: 50%;
  filter: blur(20px);
  animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.3; }
  100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.6; }
}

.progress-container {
  width: 300px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  width: 0%;
  height: 100%;
  background: var(--gradient-secondary);
  border-radius: 2px;
  position: relative;
  transition: width 0.1s ease;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
  animation: progressShine 1s ease-in-out infinite;
}

@keyframes progressShine {
  0% { transform: translateX(-20px); }
  100% { transform: translateX(20px); }
}

.progress-text {
  margin-top: 1rem;
  font-size: 1.2rem;
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

/* ===== NAVIGATION STYLES ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  padding: 1rem 0;
  background: rgba(10, 10, 15, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--glass-border);
  z-index: 1000;
  transition: all var(--transition-normal);
}

.nav-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-logo {
  position: relative;
  z-index: 2;
}

.logo-text {
  font-size: 1.8rem;
  font-weight: var(--font-weight-bold);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.logo-glow-nav {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, var(--electric-blue) 0%, transparent 70%);
  opacity: 0.2;
  border-radius: 50%;
  filter: blur(10px);
  pointer-events: none;
}

.nav-menu {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  position: relative;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all var(--transition-normal);
}

.nav-link:hover {
  color: var(--text-primary);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: var(--gradient-secondary);
  transition: width var(--transition-normal);
}

.nav-link:hover::after {
  width: 80%;
}

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: var(--text-primary);
  border-radius: 2px;
  transition: all var(--transition-normal);
}

/* ===== MAIN CONTENT ===== */
.main-content {
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-slow);
}

.main-content.loaded {
  opacity: 1;
  visibility: visible;
}

/* ===== HERO SECTION ===== */
.hero {
  position: relative;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.spline-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.7;
}

.spline-container spline-viewer {
  width: 100%;
  height: 100%;
}

.floating-orbs {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(1px);
  opacity: 0.6;
}

.orb-1 {
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, var(--electric-blue), transparent);
  top: 20%;
  left: 10%;
  animation: float1 6s ease-in-out infinite;
}

.orb-2 {
  width: 150px;
  height: 150px;
  background: radial-gradient(circle, var(--neon-purple), transparent);
  top: 60%;
  right: 15%;
  animation: float2 8s ease-in-out infinite;
}

.orb-3 {
  width: 80px;
  height: 80px;
  background: radial-gradient(circle, var(--neon-cyan), transparent);
  bottom: 30%;
  left: 20%;
  animation: float3 7s ease-in-out infinite;
}

.orb-4 {
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, var(--neon-pink), transparent);
  top: 10%;
  right: 30%;
  animation: float4 9s ease-in-out infinite;
}

@keyframes float1 {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  33% { transform: translateY(-20px) translateX(10px); }
  66% { transform: translateY(10px) translateX(-15px); }
}

@keyframes float2 {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  50% { transform: translateY(-30px) translateX(20px); }
}

@keyframes float3 {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  25% { transform: translateY(15px) translateX(-10px); }
  75% { transform: translateY(-25px) translateX(15px); }
}

@keyframes float4 {
  0%, 100% { transform: translateY(0px) translateX(0px); }
  40% { transform: translateY(-15px) translateX(-20px); }
  80% { transform: translateY(25px) translateX(10px); }
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  padding: 0 2rem;
}

.hero-text {
  margin-bottom: 3rem;
}

.hero-headline {
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: var(--font-weight-bold);
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

.line-1, .line-2 {
  display: block;
  opacity: 0;
  transform: translateY(50px);
  filter: blur(10px);
}

.line-1 {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.line-2 {
  background: var(--gradient-secondary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: clamp(1.2rem, 3vw, 1.5rem);
  color: var(--text-secondary);
  margin-bottom: 3rem;
  opacity: 0;
  transform: translateY(30px);
  filter: blur(5px);
}

.cta-button {
  position: relative;
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  border-radius: 50px;
  padding: 1rem 3rem;
  font-size: 1.2rem;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  cursor: pointer;
  backdrop-filter: blur(20px);
  transition: all var(--transition-normal);
  overflow: hidden;
  opacity: 0;
  transform: translateY(30px) scale(0.9);
}

.cta-button:hover {
  transform: translateY(-5px) scale(1.05);
  border-color: var(--electric-blue);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.3);
}

.cta-text {
  position: relative;
  z-index: 2;
}

.cta-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  background: var(--gradient-secondary);
  border-radius: 50%;
  transition: all var(--transition-normal);
  opacity: 0;
}

.cta-button:hover .cta-glow {
  width: 300px;
  height: 300px;
  opacity: 0.1;
}

/* ===== ABOUT SECTION ===== */
.about {
  padding: var(--section-padding);
  position: relative;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-left {
  display: flex;
  justify-content: center;
}

.profile-container {
  position: relative;
}

.profile-image {
  position: relative;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid var(--glass-border);
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  transition: all var(--transition-normal);
}

.profile-image:hover {
  transform: translateY(-10px) rotate(5deg);
  border-color: var(--electric-blue);
  box-shadow: 0 20px 40px rgba(0, 212, 255, 0.3);
}

.profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 350px;
  height: 350px;
  background: radial-gradient(circle, var(--electric-blue) 0%, transparent 70%);
  opacity: 0.2;
  border-radius: 50%;
  filter: blur(20px);
  pointer-events: none;
  transition: all var(--transition-normal);
}

.profile-image:hover .profile-glow {
  opacity: 0.4;
  transform: translate(-50%, -50%) scale(1.1);
}

.about-right {
  padding-left: 2rem;
}

.about-text {
  font-size: 1.2rem;
  color: var(--text-secondary);
  margin-bottom: 3rem;
  line-height: 1.8;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.skill-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 15px;
  backdrop-filter: blur(20px);
  transition: all var(--transition-normal);
  cursor: pointer;
}

.skill-item:hover {
  transform: translateY(-10px);
  border-color: var(--electric-blue);
  box-shadow: 0 15px 30px rgba(0, 212, 255, 0.2);
}

.skill-item i {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.skill-item span {
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

/* ===== PROJECTS SECTION ===== */
.projects {
  padding: var(--section-padding);
  background: linear-gradient(180deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
}

.projects-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.project-card {
  position: relative;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  transition: all var(--transition-normal);
  cursor: pointer;
  height: 400px;
}

.project-card:hover {
  transform: translateY(-15px);
  border-color: var(--electric-blue);
  box-shadow: 0 25px 50px rgba(0, 212, 255, 0.3);
}

.project-image {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.project-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all var(--transition-slow);
}

.project-card:hover .project-image img {
  transform: scale(1.1);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    180deg,
    transparent 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  align-items: flex-end;
  padding: 2rem;
  opacity: 0;
  transition: all var(--transition-normal);
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-info {
  width: 100%;
}

.project-title {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  margin-bottom: 0.5rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.project-description {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.project-tech {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.tech-tag {
  padding: 0.3rem 0.8rem;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  font-size: 0.8rem;
  color: var(--text-secondary);
  backdrop-filter: blur(10px);
}

.project-cta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--gradient-secondary);
  border: none;
  border-radius: 25px;
  padding: 0.8rem 1.5rem;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.project-cta:hover {
  transform: translateX(5px);
  box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}

.project-cta i {
  font-size: 1.2rem;
  transition: all var(--transition-normal);
}

.project-cta:hover i {
  transform: translateX(3px) translateY(-3px);
}

/* ===== CONTACT SECTION ===== */
.contact {
  padding: var(--section-padding);
  background: var(--primary-bg);
}

.contact-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-group {
  position: relative;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1.2rem 1.5rem;
  background: var(--glass-bg);
  border: 2px solid var(--glass-border);
  border-radius: 15px;
  color: var(--text-primary);
  font-family: var(--font-family);
  font-size: 1rem;
  backdrop-filter: blur(20px);
  transition: all var(--transition-normal);
  resize: vertical;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--text-muted);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--electric-blue);
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.input-glow {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 15px;
  background: var(--gradient-secondary);
  opacity: 0;
  transition: all var(--transition-normal);
  pointer-events: none;
  z-index: -1;
}

.form-group input:focus + .input-glow,
.form-group textarea:focus + .input-glow {
  opacity: 0.1;
}

.submit-button {
  position: relative;
  background: var(--gradient-primary);
  border: none;
  border-radius: 50px;
  padding: 1.2rem 3rem;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-bounce);
  overflow: hidden;
}

.submit-button:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 15px 30px rgba(139, 92, 246, 0.4);
}

.submit-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all var(--transition-normal);
}

.submit-button:hover .submit-glow {
  width: 300px;
  height: 300px;
}

.contact-info {
  padding-left: 2rem;
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.social-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 15px;
  color: var(--text-secondary);
  text-decoration: none;
  backdrop-filter: blur(20px);
  transition: all var(--transition-normal);
}

.social-link:hover {
  transform: translateX(10px);
  border-color: var(--electric-blue);
  color: var(--text-primary);
  box-shadow: 0 10px 20px rgba(0, 212, 255, 0.2);
}

.social-link i {
  font-size: 1.5rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== FOOTER SECTION ===== */
.footer {
  position: relative;
  padding: 3rem 0;
  background: var(--secondary-bg);
  border-top: 1px solid var(--glass-border);
  overflow: hidden;
}

.footer-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  border-radius: 50%;
  background: var(--gradient-secondary);
  opacity: 0.1;
  filter: blur(1px);
}

.particle-1 {
  width: 4px;
  height: 4px;
  top: 20%;
  left: 10%;
  animation: particleFloat1 8s ease-in-out infinite;
}

.particle-2 {
  width: 6px;
  height: 6px;
  top: 60%;
  left: 30%;
  animation: particleFloat2 10s ease-in-out infinite;
}

.particle-3 {
  width: 3px;
  height: 3px;
  top: 40%;
  right: 20%;
  animation: particleFloat3 12s ease-in-out infinite;
}

.particle-4 {
  width: 5px;
  height: 5px;
  bottom: 30%;
  right: 40%;
  animation: particleFloat4 9s ease-in-out infinite;
}

.particle-5 {
  width: 4px;
  height: 4px;
  top: 80%;
  left: 60%;
  animation: particleFloat5 11s ease-in-out infinite;
}

@keyframes particleFloat1 {
  0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.1; }
  50% { transform: translateY(-20px) translateX(10px); opacity: 0.3; }
}

@keyframes particleFloat2 {
  0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.1; }
  50% { transform: translateY(-30px) translateX(-15px); opacity: 0.3; }
}

@keyframes particleFloat3 {
  0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.1; }
  50% { transform: translateY(-25px) translateX(20px); opacity: 0.3; }
}

@keyframes particleFloat4 {
  0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.1; }
  50% { transform: translateY(-15px) translateX(-10px); opacity: 0.3; }
}

@keyframes particleFloat5 {
  0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.1; }
  50% { transform: translateY(-35px) translateX(5px); opacity: 0.3; }
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 2rem;
}

.footer-nav {
  display: flex;
  gap: 2rem;
}

.footer-link {
  color: var(--text-secondary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.footer-link:hover {
  color: var(--text-primary);
  transform: translateY(-2px);
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  color: var(--text-secondary);
  text-decoration: none;
  backdrop-filter: blur(20px);
  transition: all var(--transition-normal);
}

.footer-social-link:hover {
  transform: translateY(-5px);
  border-color: var(--electric-blue);
  color: var(--text-primary);
  box-shadow: 0 10px 20px rgba(0, 212, 255, 0.3);
}

.footer-text {
  color: var(--text-muted);
  font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  .about-right {
    padding-left: 0;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .contact-info {
    padding-left: 0;
  }

  .projects-container {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100vh;
    background: rgba(10, 10, 15, 0.95);
    backdrop-filter: blur(20px);
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    transition: right var(--transition-normal);
  }

  .nav-menu.active {
    right: 0;
  }

  .hamburger {
    display: flex;
  }

  .hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .hero-headline {
    font-size: clamp(2.5rem, 6vw, 4rem);
  }

  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .projects-container {
    grid-template-columns: 1fr;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-nav {
    order: 2;
  }

  .footer-social {
    order: 1;
  }

  .footer-text {
    order: 3;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .nav-container {
    padding: 0 1rem;
  }

  .hero-content {
    padding: 0 1rem;
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .profile-image {
    width: 250px;
    height: 250px;
  }

  .social-links {
    gap: 1rem;
  }

  .social-link {
    padding: 0.8rem 1rem;
  }
}
