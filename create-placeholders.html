<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Placeholder Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #0a0a0f;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-item {
            text-align: center;
        }
        canvas {
            border: 2px solid #333;
            border-radius: 10px;
            max-width: 100%;
        }
        button {
            background: linear-gradient(135deg, #0066ff, #8b5cf6);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Portfolio Placeholder Images Generator</h1>
        <p>This tool generates placeholder images for the portfolio website.</p>
        
        <button onclick="generateAllImages()">Generate All Images</button>
        <button onclick="downloadAll()">Download All</button>
        
        <div class="image-grid" id="imageGrid">
            <!-- Images will be generated here -->
        </div>
    </div>

    <script>
        const images = [
            { name: 'profile.jpg', width: 400, height: 400, type: 'profile' },
            { name: 'project-1.jpg', width: 600, height: 400, type: 'project', title: 'E-Commerce Platform' },
            { name: 'project-2.jpg', width: 600, height: 400, type: 'project', title: '3D Portfolio Website' },
            { name: 'project-3.jpg', width: 600, height: 400, type: 'project', title: 'AI Dashboard' },
            { name: 'project-4.jpg', width: 600, height: 400, type: 'project', title: 'Mobile Banking App' },
            { name: 'project-5.jpg', width: 600, height: 400, type: 'project', title: 'Social Media Platform' },
            { name: 'project-6.jpg', width: 600, height: 400, type: 'project', title: 'VR Experience' }
        ];

        function createGradient(ctx, width, height, colors) {
            const gradient = ctx.createLinearGradient(0, 0, width, height);
            colors.forEach((color, index) => {
                gradient.addColorStop(index / (colors.length - 1), color);
            });
            return gradient;
        }

        function generateImage(imageConfig) {
            const canvas = document.createElement('canvas');
            canvas.width = imageConfig.width;
            canvas.height = imageConfig.height;
            const ctx = canvas.getContext('2d');

            if (imageConfig.type === 'profile') {
                // Create profile image
                const gradient = createGradient(ctx, imageConfig.width, imageConfig.height, [
                    '#0066ff', '#8b5cf6', '#00d4ff'
                ]);
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, imageConfig.width, imageConfig.height);

                // Add circle overlay
                ctx.globalCompositeOperation = 'overlay';
                ctx.beginPath();
                ctx.arc(imageConfig.width/2, imageConfig.height/2, imageConfig.width/3, 0, 2 * Math.PI);
                ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
                ctx.fill();

                // Add text
                ctx.globalCompositeOperation = 'source-over';
                ctx.fillStyle = 'white';
                ctx.font = 'bold 48px Arial';
                ctx.textAlign = 'center';
                ctx.fillText('MILAD', imageConfig.width/2, imageConfig.height/2 + 15);
            } else {
                // Create project image
                const gradients = [
                    ['#0066ff', '#00d4ff'],
                    ['#8b5cf6', '#f472b6'],
                    ['#06ffa5', '#00d4ff'],
                    ['#ff6b35', '#f472b6'],
                    ['#8b5cf6', '#0066ff'],
                    ['#00d4ff', '#06ffa5']
                ];
                
                const colorIndex = parseInt(imageConfig.name.split('-')[1]) - 1;
                const gradient = createGradient(ctx, imageConfig.width, imageConfig.height, gradients[colorIndex]);
                
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, imageConfig.width, imageConfig.height);

                // Add geometric shapes
                ctx.globalCompositeOperation = 'overlay';
                for (let i = 0; i < 5; i++) {
                    ctx.beginPath();
                    ctx.arc(
                        Math.random() * imageConfig.width,
                        Math.random() * imageConfig.height,
                        Math.random() * 100 + 20,
                        0, 2 * Math.PI
                    );
                    ctx.fillStyle = `rgba(255, 255, 255, ${Math.random() * 0.1 + 0.05})`;
                    ctx.fill();
                }

                // Add title
                ctx.globalCompositeOperation = 'source-over';
                ctx.fillStyle = 'white';
                ctx.font = 'bold 32px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(imageConfig.title, imageConfig.width/2, imageConfig.height/2);
                
                // Add subtitle
                ctx.font = '18px Arial';
                ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
                ctx.fillText('Portfolio Project', imageConfig.width/2, imageConfig.height/2 + 40);
            }

            return canvas;
        }

        function generateAllImages() {
            const grid = document.getElementById('imageGrid');
            grid.innerHTML = '';

            images.forEach(imageConfig => {
                const canvas = generateImage(imageConfig);
                
                const container = document.createElement('div');
                container.className = 'image-item';
                
                const title = document.createElement('h3');
                title.textContent = imageConfig.name;
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = 'Download';
                downloadBtn.onclick = () => downloadImage(canvas, imageConfig.name);
                
                container.appendChild(title);
                container.appendChild(canvas);
                container.appendChild(downloadBtn);
                
                grid.appendChild(container);
            });
        }

        function downloadImage(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/jpeg', 0.9);
            link.click();
        }

        function downloadAll() {
            const canvases = document.querySelectorAll('canvas');
            canvases.forEach((canvas, index) => {
                setTimeout(() => {
                    downloadImage(canvas, images[index].name);
                }, index * 500);
            });
        }

        // Generate images on load
        window.onload = generateAllImages;
    </script>
</body>
</html>
