# 🚀 GitHub Deployment Guide

## Phase 1: Create GitHub Repository

### Step 1: Create Repository
1. Go to [GitHub.com](https://github.com) and sign in
2. Click the "+" icon → "New repository"
3. Repository name: `adarsh-portfolio` (or your preferred name)
4. Description: "Futuristic Portfolio Website with GSAP Animations"
5. Make it **Public** (required for free GitHub Pages)
6. ✅ Check "Add a README file"
7. Click "Create repository"

### Step 2: Clone Repository Locally
```bash
git clone https://github.com/YOUR_USERNAME/adarsh-portfolio.git
cd adarsh-portfolio
```

## Phase 2: Upload Portfolio Files

### Option A: Direct Upload (Easiest)
1. Download all files from the `dist/` folder
2. Go to your GitHub repository page
3. Click "uploading an existing file"
4. Drag and drop all files from `dist/` folder
5. Commit with message: "Add portfolio website files"

### Option B: Git Commands
```bash
# Copy all files from dist/ to your repository folder
# Then:
git add .
git commit -m "Add portfolio website files"
git push origin main
```

## Phase 3: Enable GitHub Pages

1. Go to your repository on GitHub
2. Click "Settings" tab
3. Scroll down to "Pages" in the left sidebar
4. Under "Source", select "Deploy from a branch"
5. Branch: **main**
6. Folder: **/ (root)**
7. Click "Save"

Your site will be available at:
`https://YOUR_USERNAME.github.io/adarsh-portfolio`

## Phase 4: Automatic Deployment (Optional)

If you want automatic deployment when you make changes:

1. Create `.github/workflows/deploy.yml` in your repository
2. Use the workflow file I've created
3. Any push to main branch will automatically rebuild and deploy

## Files to Upload to GitHub

From your `dist/` folder, upload these files:
- `index.html` (main page)
- `style.css` (styles)
- `main.js` (JavaScript)
- `profile.svg` (profile image)
- `project-1.svg` through `project-6.svg` (project images)
- `vite.svg` (favicon)

## Troubleshooting

### Issue: Site shows 404
- Make sure `index.html` is in the root of your repository
- Check that GitHub Pages is enabled
- Wait 5-10 minutes for deployment

### Issue: Animations not working
- Check browser console for errors
- Ensure CDN links are working
- Try hard refresh (Ctrl+F5)

### Issue: Images not loading
- Verify all SVG files are uploaded
- Check file names match exactly

## Next Steps After Deployment

1. **Test your live site** thoroughly
2. **Share the URL** with others
3. **Customize content** with your real information
4. **Add real project images**
5. **Update social media links**

## Custom Domain (Optional)

To use your own domain:
1. Add a `CNAME` file to your repository with your domain
2. Configure DNS with your domain provider
3. Point CNAME record to `YOUR_USERNAME.github.io`

---

**Ready to deploy? Let me know when you've created the GitHub repository and I'll help with the next steps!**
